#!/usr/bin/env python3
"""
Simple test script to verify the backend is working
"""
import requests
import os

def test_backend():
    """Test if the backend server is running and responding"""
    backend_url = "http://127.0.0.1:5000"
    
    print("🔍 Testing YOLO Backend Server...")
    print(f"Backend URL: {backend_url}")
    
    try:
        # Test if server is running
        response = requests.get(f"{backend_url}/", timeout=5)
        print("❌ Server responded but no root route defined (this is expected)")
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server!")
        print("💡 Make sure to start the backend with: python backend/app.py")
        return False
    except requests.exceptions.Timeout:
        print("❌ Server timeout!")
        return False
    except Exception as e:
        print(f"✅ Server is running (got expected 404 for root route)")
    
    # Test if we have test images
    uploads_dir = "uploads"
    if os.path.exists(uploads_dir):
        test_images = [f for f in os.listdir(uploads_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        if test_images:
            print(f"✅ Found {len(test_images)} test images in uploads directory")
            print(f"📁 Sample images: {test_images[:3]}")
        else:
            print("⚠️  No test images found in uploads directory")
    else:
        print("⚠️  Uploads directory not found")
    
    # Check model file
    model_path = "model/best.pt"
    if os.path.exists(model_path):
        print("✅ YOLO model file found")
    else:
        print("❌ YOLO model file not found at model/best.pt")
        return False
    
    print("\n🎯 Backend Status: READY")
    print("📝 Instructions:")
    print("1. Open frontend/index.html in your browser")
    print("2. Upload an image")
    print("3. Click 'Detect Objects'")
    print("4. View the results!")
    
    return True

if __name__ == "__main__":
    test_backend()
