from flask import Flask, request, jsonify, send_file
from ultralytics import YOLO
import os
import uuid

app = Flask(__name__)
model = YOLO(r"D:\YOLO\yolo_music\yolo_app\model\best.pt")  # Path to your trained model

UPLOAD_FOLDER = "uploads"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

@app.route("/predict", methods=["POST"])
def predict():
    if "image" not in request.files:
        return jsonify({"error": "No image uploaded"}), 400

    file = request.files["image"]
    filename = f"{uuid.uuid4().hex}_{file.filename}"
    image_path = os.path.join(UPLOAD_FOLDER, filename)
    file.save(image_path)

    results = model.predict(image_path, save=True, save_txt=False)
    result_image = results[0].save_path

    # Get confidence scores and labels
    detections = []
    for box in results[0].boxes:
        cls_id = int(box.cls[0])
        score = float(box.conf[0])
        label = model.names[cls_id]
        detections.append({"label": label, "score": round(score, 2)})

    return jsonify({
        "image_path": result_image.replace("\\", "/"),
        "detections": detections
    })

@app.route("/uploads/<path:filename>")
def serve_file(filename):
    return send_file(os.path.join(UPLOAD_FOLDER, filename))

if __name__ == "__main__":
    app.run(debug=True)
