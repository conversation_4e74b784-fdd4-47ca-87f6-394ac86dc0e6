from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from ultralytics import YOLO
import os
import uuid
import glob
from pathlib import Path

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes
model = YOLO("../model/best.pt")  # Path to your trained model

UPLOAD_FOLDER = "uploads"
RESULTS_FOLDER = "runs"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(RESULTS_FOLDER, exist_ok=True)

def get_latest_predict_folder():
    """Find the latest predict folder in runs/detect/"""
    detect_path = os.path.join(RESULTS_FOLDER, "detect")
    if not os.path.exists(detect_path):
        return None

    # Find all predict folders (predict, predict2, predict3, etc.)
    predict_folders = glob.glob(os.path.join(detect_path, "predict*"))
    if not predict_folders:
        return None

    # Sort by modification time to get the latest
    latest_folder = max(predict_folders, key=os.path.getmtime)
    return latest_folder

@app.route("/predict", methods=["POST"])
def predict():
    print("🔍 Received prediction request")
    if "image" not in request.files:
        print("❌ No image in request")
        return jsonify({"error": "No image uploaded"}), 400

    file = request.files["image"]
    filename = f"{uuid.uuid4().hex}_{file.filename}"
    image_path = os.path.join(UPLOAD_FOLDER, filename)
    file.save(image_path)
    print(f"📁 Saved image to: {image_path}")

    print("🤖 Running YOLO prediction...")
    results = model.predict(image_path, save=True, save_txt=False)
    result_image_path = results[0].save_path
    print(f"💾 Result image saved to: {result_image_path}")

    # Extract just the filename from the full path
    result_filename = os.path.basename(result_image_path)
    print(f"📄 Result filename: {result_filename}")

    # Get the predict folder name (predict, predict2, etc.)
    predict_folder_name = os.path.basename(os.path.dirname(result_image_path))
    print(f"📁 Predict folder: {predict_folder_name}")

    # Get confidence scores and labels
    detections = []
    if results[0].boxes is not None:
        for box in results[0].boxes:
            cls_id = int(box.cls[0])
            score = float(box.conf[0])
            label = model.names[cls_id]
            detections.append({"label": label, "score": round(score, 2)})

    print(f"🎯 Found {len(detections)} detections: {detections}")

    response_data = {
        "result_filename": result_filename,
        "predict_folder": predict_folder_name,
        "detections": detections,
        "message": f"Found {len(detections)} objects" if detections else "No objects detected"
    }
    print(f"📤 Sending response: {response_data}")
    return jsonify(response_data)

@app.route("/uploads/<path:filename>")
def serve_upload_file(filename):
    return send_file(os.path.join(UPLOAD_FOLDER, filename))

@app.route("/debug/results")
def debug_results():
    """Debug endpoint to list all available result images"""
    results = []
    detect_path = os.path.join(RESULTS_FOLDER, "detect")

    if os.path.exists(detect_path):
        predict_folders = glob.glob(os.path.join(detect_path, "predict*"))
        for folder in predict_folders:
            folder_name = os.path.basename(folder)
            images = glob.glob(os.path.join(folder, "*.jpg"))
            for img in images:
                img_name = os.path.basename(img)
                results.append({
                    "folder": folder_name,
                    "filename": img_name,
                    "url": f"/results/{folder_name}/{img_name}",
                    "fallback_url": f"/results/{img_name}"
                })

    return jsonify({
        "total_results": len(results),
        "results": results,
        "latest_folder": os.path.basename(get_latest_predict_folder()) if get_latest_predict_folder() else None
    })

@app.route("/results/<predict_folder>/<filename>")
def serve_result_file(predict_folder, filename):
    # Serve files from the runs/detect/{predict_folder} directory
    file_path = os.path.join(RESULTS_FOLDER, "detect", predict_folder, filename)
    print(f"🖼️  Serving result image: {filename} from {predict_folder}")
    print(f"📂 Full path: {file_path}")
    print(f"📁 File exists: {os.path.exists(file_path)}")

    if os.path.exists(file_path):
        print("✅ Sending file")
        return send_file(file_path)
    else:
        print("❌ File not found")
        return jsonify({"error": "Result image not found"}), 404

@app.route("/results/<filename>")
def serve_result_file_fallback(filename):
    # Fallback: try to find the file in the latest predict folder
    print(f"🔍 Looking for {filename} in latest predict folder...")
    latest_folder = get_latest_predict_folder()

    if latest_folder:
        file_path = os.path.join(latest_folder, filename)
        print(f"📂 Trying path: {file_path}")

        if os.path.exists(file_path):
            print("✅ Found file in latest folder")
            return send_file(file_path)

    print("❌ File not found in any predict folder")
    return jsonify({"error": "Result image not found"}), 404

if __name__ == "__main__":
    app.run(debug=True)
