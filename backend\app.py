from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from ultralytics import YOLO
import os
import uuid

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes
model = YOLO("../model/best.pt")  # Path to your trained model

UPLOAD_FOLDER = "uploads"
RESULTS_FOLDER = "runs"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(RESULTS_FOLDER, exist_ok=True)

@app.route("/predict", methods=["POST"])
def predict():
    if "image" not in request.files:
        return jsonify({"error": "No image uploaded"}), 400

    file = request.files["image"]
    filename = f"{uuid.uuid4().hex}_{file.filename}"
    image_path = os.path.join(UPLOAD_FOLDER, filename)
    file.save(image_path)

    results = model.predict(image_path, save=True, save_txt=False)
    result_image_path = results[0].save_path

    # Extract just the filename from the full path
    result_filename = os.path.basename(result_image_path)

    # Get confidence scores and labels
    detections = []
    if results[0].boxes is not None:
        for box in results[0].boxes:
            cls_id = int(box.cls[0])
            score = float(box.conf[0])
            label = model.names[cls_id]
            detections.append({"label": label, "score": round(score, 2)})

    return jsonify({
        "result_filename": result_filename,
        "detections": detections,
        "message": f"Found {len(detections)} objects" if detections else "No objects detected"
    })

@app.route("/uploads/<path:filename>")
def serve_upload_file(filename):
    return send_file(os.path.join(UPLOAD_FOLDER, filename))

@app.route("/results/<path:filename>")
def serve_result_file(filename):
    # Serve files from the runs/detect/predict directory
    file_path = os.path.join(RESULTS_FOLDER, "detect", "predict", filename)
    if os.path.exists(file_path):
        return send_file(file_path)
    else:
        return jsonify({"error": "Result image not found"}), 404

if __name__ == "__main__":
    app.run(debug=True)
