<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>YOLO Debug Interface</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    button { margin: 10px; padding: 10px 20px; }
    .debug-section { border: 1px solid #ccc; margin: 10px 0; padding: 15px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    #debugLog { background: #f5f5f5; padding: 10px; height: 200px; overflow-y: scroll; }
  </style>
</head>
<body>
  <h1>🔧 YOLO Debug Interface</h1>
  
  <div class="debug-section">
    <h3>🧪 Connection Tests</h3>
    <button onclick="testBackend()">Test Backend Connection</button>
    <button onclick="testDebugEndpoint()">Test Debug Endpoint</button>
    <button onclick="testExistingImage()">Test Existing Result Image</button>
  </div>
  
  <div class="debug-section">
    <h3>📤 Upload Test</h3>
    <input type="file" id="testImage" accept="image/*">
    <button onclick="testUpload()">Test Upload & Detection</button>
  </div>
  
  <div class="debug-section">
    <h3>📺 Display Test</h3>
    <button onclick="showTestResults()">Show Test Results</button>
    <button onclick="hideResults()">Hide Results</button>
  </div>
  
  <div class="debug-section">
    <h3>📋 Debug Log</h3>
    <div id="debugLog"></div>
    <button onclick="clearLog()">Clear Log</button>
  </div>
  
  <div id="results" style="display: none; border: 2px solid green; padding: 20px; margin: 20px 0;">
    <h2>📊 Detection Results</h2>
    <img id="resultImage" style="max-width: 400px; border: 1px solid #ddd;" alt="Result will appear here" />
    <h3>🏷️ Detected Objects:</h3>
    <ul id="detections"></ul>
  </div>

  <script>
    function log(message, type = 'info') {
      const logDiv = document.getElementById('debugLog');
      const timestamp = new Date().toLocaleTimeString();
      const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
      logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(message);
    }

    function clearLog() {
      document.getElementById('debugLog').innerHTML = '';
    }

    async function testBackend() {
      log('🧪 Testing backend connection...');
      try {
        const response = await fetch('http://127.0.0.1:5000/', { method: 'GET' });
        log(`Backend responded with status: ${response.status}`, 'success');
      } catch (error) {
        log(`❌ Backend connection failed: ${error.message}`, 'error');
      }
    }

    async function testDebugEndpoint() {
      log('🔍 Testing debug endpoint...');
      try {
        const response = await fetch('http://127.0.0.1:5000/debug/results');
        if (response.ok) {
          const data = await response.json();
          log(`✅ Debug endpoint works! Found ${data.total_results} results`, 'success');
          log(`Latest folder: ${data.latest_folder}`, 'info');
        } else {
          log(`❌ Debug endpoint error: ${response.status}`, 'error');
        }
      } catch (error) {
        log(`❌ Debug endpoint failed: ${error.message}`, 'error');
      }
    }

    async function testExistingImage() {
      log('🖼️ Testing existing result image...');
      const testUrl = 'http://127.0.0.1:5000/results/predict4/11bf6289d32e465882053049c9c3f6a3_test4.jpg';
      
      try {
        const response = await fetch(testUrl);
        if (response.ok) {
          log('✅ Existing image accessible', 'success');
          document.getElementById('resultImage').src = testUrl;
          document.getElementById('results').style.display = 'block';
        } else {
          log(`❌ Image not accessible: ${response.status}`, 'error');
        }
      } catch (error) {
        log(`❌ Image test failed: ${error.message}`, 'error');
      }
    }

    async function testUpload() {
      const input = document.getElementById('testImage');
      if (!input.files.length) {
        log('❌ Please select an image first', 'error');
        return;
      }

      log('📤 Testing upload and detection...');
      
      try {
        const formData = new FormData();
        formData.append('image', input.files[0]);

        const response = await fetch('http://127.0.0.1:5000/predict', {
          method: 'POST',
          body: formData
        });

        log(`Upload response status: ${response.status}`, response.ok ? 'success' : 'error');
        
        if (response.ok) {
          const data = await response.json();
          log(`✅ Upload successful!`, 'success');
          log(`Result filename: ${data.result_filename}`, 'info');
          log(`Predict folder: ${data.predict_folder}`, 'info');
          log(`Detections: ${data.detections.length}`, 'info');
          
          // Show the result
          showResults(data);
        } else {
          const errorText = await response.text();
          log(`❌ Upload failed: ${errorText}`, 'error');
        }
      } catch (error) {
        log(`❌ Upload error: ${error.message}`, 'error');
      }
    }

    function showResults(data) {
      log('📺 Displaying results...');
      
      const resultsDiv = document.getElementById('results');
      resultsDiv.style.display = 'block';
      
      if (data && data.result_filename) {
        const imageUrl = data.predict_folder 
          ? `http://127.0.0.1:5000/results/${data.predict_folder}/${data.result_filename}`
          : `http://127.0.0.1:5000/results/${data.result_filename}`;
        
        log(`Setting image URL: ${imageUrl}`, 'info');
        document.getElementById('resultImage').src = imageUrl;
        
        const ul = document.getElementById('detections');
        ul.innerHTML = '';
        
        if (data.detections && data.detections.length > 0) {
          data.detections.forEach(d => {
            const li = document.createElement('li');
            li.textContent = `${d.label}: ${(d.score * 100).toFixed(1)}% confidence`;
            ul.appendChild(li);
          });
        } else {
          const li = document.createElement('li');
          li.textContent = 'No objects detected';
          ul.appendChild(li);
        }
        
        log('✅ Results displayed', 'success');
      }
    }

    function showTestResults() {
      log('📺 Showing test results...');
      const testData = {
        result_filename: '11bf6289d32e465882053049c9c3f6a3_test4.jpg',
        predict_folder: 'predict4',
        detections: [
          { label: 'person', score: 0.95 },
          { label: 'car', score: 0.87 }
        ]
      };
      showResults(testData);
    }

    function hideResults() {
      document.getElementById('results').style.display = 'none';
      log('📺 Results hidden');
    }

    // Initialize
    log('🚀 Debug interface loaded');
  </script>
</body>
</html>
