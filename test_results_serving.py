#!/usr/bin/env python3
"""
Test script to verify the results serving functionality
"""
import os
import glob

def test_results_structure():
    """Test the results directory structure"""
    print("🔍 Testing Results Directory Structure...")
    
    # Check if runs directory exists
    runs_dir = "backend/runs"
    if not os.path.exists(runs_dir):
        print("❌ Backend runs directory not found")
        return False
    
    # Check detect directory
    detect_dir = os.path.join(runs_dir, "detect")
    if not os.path.exists(detect_dir):
        print("❌ Detect directory not found")
        return False
    
    # Find all predict folders
    predict_folders = glob.glob(os.path.join(detect_dir, "predict*"))
    print(f"📁 Found {len(predict_folders)} predict folders:")
    
    for folder in predict_folders:
        folder_name = os.path.basename(folder)
        images = glob.glob(os.path.join(folder, "*.jpg"))
        print(f"   📂 {folder_name}: {len(images)} images")
        
        # Show sample images
        if images:
            for img in images[:3]:  # Show first 3 images
                img_name = os.path.basename(img)
                print(f"      🖼️  {img_name}")
    
    if predict_folders:
        # Get the latest folder
        latest_folder = max(predict_folders, key=os.path.getmtime)
        latest_name = os.path.basename(latest_folder)
        print(f"✅ Latest predict folder: {latest_name}")
        
        # Check if it has images
        latest_images = glob.glob(os.path.join(latest_folder, "*.jpg"))
        if latest_images:
            print(f"✅ Latest folder has {len(latest_images)} result images")
            return True
        else:
            print("⚠️  Latest folder has no images")
            return False
    else:
        print("❌ No predict folders found")
        return False

def test_url_structure():
    """Test the new URL structure"""
    print("\n🌐 Testing URL Structure...")
    
    # Example URLs that should work
    example_urls = [
        "http://127.0.0.1:5000/results/predict2/example.jpg",
        "http://127.0.0.1:5000/results/predict3/example.jpg",
        "http://127.0.0.1:5000/results/example.jpg"  # fallback
    ]
    
    print("📝 New URL patterns:")
    for url in example_urls:
        print(f"   🔗 {url}")
    
    print("\n💡 Frontend will now use:")
    print("   🖼️  http://127.0.0.1:5000/results/{predict_folder}/{filename}")
    print("   🔄 With fallback to: http://127.0.0.1:5000/results/{filename}")

if __name__ == "__main__":
    print("🧪 Testing YOLO Results Serving System\n")
    
    structure_ok = test_results_structure()
    test_url_structure()
    
    print(f"\n🎯 Results Structure: {'✅ READY' if structure_ok else '❌ NEEDS SETUP'}")
    print("\n📋 Next Steps:")
    print("1. 🌐 Open frontend/index.html in browser")
    print("2. 📁 Upload an image")
    print("3. 🔍 Click 'Detect Objects'")
    print("4. 👀 Check browser console (F12) for detailed logs")
    print("5. 📺 Results should now appear correctly!")
