<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>YOLO Object Detector</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .upload-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 2px dashed #ccc;
      border-radius: 10px;
      text-align: center;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
      margin-left: 10px;
    }
    button:hover {
      background-color: #45a049;
    }
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    #resultImage {
      max-width: 100%;
      height: auto;
      border: 1px solid #ddd;
      border-radius: 5px;
      margin: 10px 0;
    }
    #detections {
      background-color: #f9f9f9;
      padding: 15px;
      border-radius: 5px;
      border-left: 4px solid #4CAF50;
    }
    #detections li {
      margin: 5px 0;
      font-weight: bold;
    }
    #results {
      display: none;
    }
  </style>
</head>
<body>
  <h1>🎯 YOLO Object Detector</h1>

  <div class="upload-section">
    <h2>Upload an Image for Object Detection</h2>
    <label for="imageInput">Choose an image to detect objects:</label>
    <input type="file" id="imageInput" accept="image/*" title="Select an image file for object detection" placeholder="Select image file" />
  <div id="results">
    <h2>📊 Detection Results</h2>
    <img id="resultImage" alt="Detection result will appear here" />
    <h3>🏷️ Detected Objects:</h3>
    <ul id="detections"></ul>
  </div>
    <h3>🏷️ Detected Objects:</h3>
    <ul id="detections"></ul>
  </div>

  <script src="script.js"></script>
</body>
</html>
