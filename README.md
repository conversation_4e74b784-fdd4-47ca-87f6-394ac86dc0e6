# YOLO Object Detection Web App

This is a web application that uses <PERSON><PERSON><PERSON> (You Only Look Once) for object detection. Users can upload images through a web interface and see the detection results with bounding boxes and confidence scores.

## Project Structure

```
yolo_app/
├── backend/
│   ├── app.py              # Flask backend server
│   ├── requirements.txt    # Python dependencies
│   └── uploads/           # Uploaded images storage
├── frontend/
│   ├── index.html         # Web interface
│   └── script.js          # Frontend JavaScript
├── model/
│   └── best.pt           # YOLO model weights
└── runs/                 # YOLO detection results
```

## Features

- **Web Interface**: Clean, responsive HTML interface for image upload
- **Real-time Detection**: YOLO model processes images and returns results
- **Visual Results**: Displays processed images with bounding boxes
- **Detection Details**: Shows detected objects with confidence scores
- **CORS Support**: Frontend and backend can run on different ports

## Setup and Installation

### Prerequisites
- Python 3.8+
- pip package manager

### Backend Setup

1. Navigate to the project directory:
   ```bash
   cd yolo_app
   ```

2. Install required dependencies:
   ```bash
   pip install -r backend/requirements.txt
   ```

3. Start the Flask backend server:
   ```bash
   python backend/app.py
   ```

   The server will start on `http://127.0.0.1:5000`

### Frontend Setup

1. Open the frontend HTML file in a web browser:
   ```
   file:///path/to/yolo_app/frontend/index.html
   ```

   Or serve it using a simple HTTP server:
   ```bash
   cd frontend
   python -m http.server 8000
   ```

## Usage

1. **Start the Backend**: Make sure the Flask server is running on port 5000
2. **Open Frontend**: Open the HTML file in your web browser
3. **Upload Image**: Click "Choose File" and select an image
4. **Detect Objects**: Click "🔍 Detect Objects" button
5. **View Results**: The processed image with bounding boxes will appear along with a list of detected objects and their confidence scores

## API Endpoints

### POST /predict
Processes an uploaded image and returns detection results.

**Request:**
- Method: POST
- Content-Type: multipart/form-data
- Body: image file with key "image"

**Response:**
```json
{
  "result_filename": "detected_image.jpg",
  "detections": [
    {
      "label": "person",
      "score": 0.95
    }
  ],
  "message": "Found 1 objects"
}
```

### GET /results/<filename>
Serves the processed result images with bounding boxes.

### GET /uploads/<filename>
Serves the original uploaded images.

## Technical Details

- **Backend**: Flask with CORS support
- **Model**: YOLO v8 (ultralytics)
- **Frontend**: Vanilla HTML/CSS/JavaScript
- **Image Processing**: PIL/Pillow
- **File Handling**: UUID-based filenames to prevent conflicts

## Troubleshooting

1. **Server not starting**: Check if all dependencies are installed
2. **CORS errors**: Make sure flask-cors is installed and CORS is enabled
3. **Model loading errors**: Verify the model file exists at `model/best.pt`
4. **Image not displaying**: Check browser console for network errors

## Future Improvements

- Add support for video processing
- Implement batch image processing
- Add model selection options
- Include confidence threshold adjustment
- Add export functionality for results
