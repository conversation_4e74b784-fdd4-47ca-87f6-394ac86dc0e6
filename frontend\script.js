async function uploadImage() {
  const input = document.getElementById("imageInput");
  if (!input.files.length) return;

  const formData = new FormData();
  formData.append("image", input.files[0]);

  const res = await fetch("http://127.0.0.1:5000/predict", {
    method: "POST",
    body: formData
  });

  const data = await res.json();
  if (data.image_path) {
    document.getElementById("resultImage").src = `http://127.0.0.1:5000/${data.image_path}`;
    
    const detections = data.detections;
    const ul = document.getElementById("detections");
    ul.innerHTML = "";
    detections.forEach(d => {
      const li = document.createElement("li");
      li.textContent = `${d.label}: ${d.score}`;
      ul.appendChild(li);
    });
  } else {
    alert("Detection failed.");
  }
}
