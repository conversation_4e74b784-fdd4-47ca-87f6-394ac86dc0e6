async function uploadImage() {
  const input = document.getElementById("imageInput");
  if (!input.files.length) {
    alert("Please select an image first!");
    return;
  }

  // Show loading state
  const button = document.querySelector("button");
  const originalText = button.textContent;
  button.textContent = "Processing...";
  button.disabled = true;

  try {
    const formData = new FormData();
    formData.append("image", input.files[0]);

    console.log("Sending request to backend...");
    const res = await fetch("http://127.0.0.1:5000/predict", {
      method: "POST",
      body: formData,
      mode: 'cors'
    });

    console.log("Response status:", res.status);

    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }

    const data = await res.json();
    console.log("Response data:", data);

    if (data.result_filename) {
      console.log("✅ Got result filename:", data.result_filename);

      // Show results section
      const resultsDiv = document.getElementById("results");
      resultsDiv.style.display = "block";
      console.log("📺 Results section shown");

      // Display the result image using the new route
      const imageUrl = `http://127.0.0.1:5000/results/${data.result_filename}`;
      console.log("🖼️  Setting image URL:", imageUrl);

      const resultImage = document.getElementById("resultImage");
      resultImage.src = imageUrl;

      // Add image load event listeners for debugging
      resultImage.onload = function() {
        console.log("✅ Image loaded successfully");
      };
      resultImage.onerror = function() {
        console.error("❌ Failed to load image:", imageUrl);
      };

      // Display detections
      const detections = data.detections || [];
      console.log("🎯 Processing detections:", detections);

      const ul = document.getElementById("detections");
      ul.innerHTML = "";

      if (detections.length > 0) {
        detections.forEach(d => {
          const li = document.createElement("li");
          li.textContent = `${d.label}: ${(d.score * 100).toFixed(1)}% confidence`;
          ul.appendChild(li);
        });
        console.log(`✅ Added ${detections.length} detection items to list`);
      } else {
        const li = document.createElement("li");
        li.textContent = "No objects detected";
        li.style.color = "#666";
        li.style.fontStyle = "italic";
        ul.appendChild(li);
        console.log("ℹ️  No detections found");
      }

      // Show success message
      console.log("📝 Backend message:", data.message);
    } else if (data.error) {
      alert(`Error: ${data.error}`);
    } else {
      alert("Detection failed - no result received.");
    }
  } catch (error) {
    console.error("Error:", error);
    alert("Failed to connect to the server. Make sure the backend is running.");
  } finally {
    // Reset button state
    button.textContent = originalText;
    button.disabled = false;
  }
}
