async function uploadImage() {
  const input = document.getElementById("imageInput");
  if (!input.files.length) {
    alert("Please select an image first!");
    return;
  }

  // Show loading state
  const button = document.querySelector("button");
  const originalText = button.textContent;
  button.textContent = "Processing...";
  button.disabled = true;

  try {
    const formData = new FormData();
    formData.append("image", input.files[0]);

    const res = await fetch("http://127.0.0.1:5000/predict", {
      method: "POST",
      body: formData
    });

    const data = await res.json();

    if (data.result_filename) {
      // Show results section
      document.getElementById("results").style.display = "block";

      // Display the result image using the new route
      document.getElementById("resultImage").src = `http://127.0.0.1:5000/results/${data.result_filename}`;

      // Display detections
      const detections = data.detections || [];
      const ul = document.getElementById("detections");
      ul.innerHTML = "";

      if (detections.length > 0) {
        detections.forEach(d => {
          const li = document.createElement("li");
          li.textContent = `${d.label}: ${(d.score * 100).toFixed(1)}% confidence`;
          ul.appendChild(li);
        });
      } else {
        const li = document.createElement("li");
        li.textContent = "No objects detected";
        li.style.color = "#666";
        li.style.fontStyle = "italic";
        ul.appendChild(li);
      }

      // Show success message
      console.log(data.message);
    } else if (data.error) {
      alert(`Error: ${data.error}`);
    } else {
      alert("Detection failed - no result received.");
    }
  } catch (error) {
    console.error("Error:", error);
    alert("Failed to connect to the server. Make sure the backend is running.");
  } finally {
    // Reset button state
    button.textContent = originalText;
    button.disabled = false;
  }
}
