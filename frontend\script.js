async function uploadImage() {
  const input = document.getElementById("imageInput");
  if (!input.files.length) {
    alert("Please select an image first!");
    return;
  }

  // Show loading state
  const button = document.querySelector("button");
  const originalText = button.textContent;
  button.textContent = "Processing...";
  button.disabled = true;

  try {
    const formData = new FormData();
    formData.append("image", input.files[0]);

    console.log("Sending request to backend...");
    const res = await fetch("http://127.0.0.1:5000/predict", {
      method: "POST",
      body: formData,
      mode: 'cors'
    });

    console.log("Response status:", res.status);

    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }

    const data = await res.json();
    console.log("Response data:", data);

    if (data.result_filename) {
      console.log("✅ Got result filename:", data.result_filename);
      console.log("📁 Predict folder:", data.predict_folder);

      // Show results section
      const resultsDiv = document.getElementById("results");
      resultsDiv.style.display = "block";
      console.log("📺 Results section shown");

      // Display the result image using the new route with predict folder
      let imageUrl;
      if (data.predict_folder) {
        imageUrl = `http://127.0.0.1:5000/results/${data.predict_folder}/${data.result_filename}`;
      } else {
        // Fallback to the old route
        imageUrl = `http://127.0.0.1:5000/results/${data.result_filename}`;
      }
      console.log("🖼️  Setting image URL:", imageUrl);

      const resultImage = document.getElementById("resultImage");
      resultImage.src = imageUrl;

      // Add image load event listeners for debugging
      resultImage.onload = function() {
        console.log("✅ Image loaded successfully");
      };
      resultImage.onerror = function() {
        console.error("❌ Failed to load image:", imageUrl);
      };

      // Display detections
      const detections = data.detections || [];
      console.log("🎯 Processing detections:", detections);

      const ul = document.getElementById("detections");
      ul.innerHTML = "";

      if (detections.length > 0) {
        detections.forEach(d => {
          const li = document.createElement("li");
          li.textContent = `${d.label}: ${(d.score * 100).toFixed(1)}% confidence`;
          ul.appendChild(li);
        });
        console.log(`✅ Added ${detections.length} detection items to list`);
      } else {
        const li = document.createElement("li");
        li.textContent = "No objects detected";
        li.style.color = "#666";
        li.style.fontStyle = "italic";
        ul.appendChild(li);
        console.log("ℹ️  No detections found");
      }

      // Show success message
      console.log("📝 Backend message:", data.message);
    } else if (data.error) {
      alert(`Error: ${data.error}`);
    } else {
      alert("Detection failed - no result received.");
    }
  } catch (error) {
    console.error("❌ Upload failed:", error);
    console.error("Error details:", {
      name: error.name,
      message: error.message,
      stack: error.stack
    });
    alert(`❌ Upload failed: ${error.message}\n\nCheck browser console (F12) for details.`);
  } finally {
    // Reset button state
    button.textContent = originalText;
    button.disabled = false;
    console.log("🔄 Button state reset");
  }
}

async function testConnection() {
  console.log("🧪 Testing backend connection...");

  try {
    const response = await fetch("http://127.0.0.1:5000/debug/results", {
      method: "GET"
    });

    if (response.ok) {
      const data = await response.json();
      console.log("✅ Backend connection successful!");
      console.log("📊 Available results:", data);
      alert(`✅ Backend connected! Found ${data.total_results} result images in ${data.latest_folder || 'no'} folder.`);
    } else {
      console.error("❌ Backend responded with error:", response.status);
      alert(`❌ Backend error: ${response.status}`);
    }
  } catch (error) {
    console.error("❌ Connection failed:", error);
    alert("❌ Cannot connect to backend. Make sure it's running on port 5000.");
  }
}

function showTestResult() {
  console.log("🧪 Showing test result...");

  // Show results section
  const resultsDiv = document.getElementById("results");
  resultsDiv.style.display = "block";
  console.log("📺 Results section shown");

  // Set a test image (using one of the existing results)
  const resultImage = document.getElementById("resultImage");
  resultImage.src = "http://127.0.0.1:5000/results/predict4/11bf6289d32e465882053049c9c3f6a3_test4.jpg";

  // Add test detections
  const ul = document.getElementById("detections");
  ul.innerHTML = "";

  const testDetections = [
    { label: "person", score: 0.95 },
    { label: "car", score: 0.87 }
  ];

  testDetections.forEach(d => {
    const li = document.createElement("li");
    li.textContent = `${d.label}: ${(d.score * 100).toFixed(1)}% confidence`;
    ul.appendChild(li);
  });

  console.log("✅ Test result displayed");
}
